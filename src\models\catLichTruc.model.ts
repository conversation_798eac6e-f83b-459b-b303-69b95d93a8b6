import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DMLoaiHinhTruc } from './dmLoaiHinhTruc.model';
import EntityModel from './entity.model';
import { LichTruc } from './lichTruc.model';
import { NgayTruc } from './ngayTruc.model';
import { Organization } from './organization.model';

@Entity()
export class CatLichTruc extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  idNgayTruc?: string;
  @ManyToOne(() => NgayTruc)
  @JoinColumn({ name: 'idNgayTruc' })
  ngayTruc?: NgayTruc;

  @Column({
    type: 'uuid',
    nullable: true,
  })
  idLichTruc?: string;
  @ManyToOne(() => LichTruc)
  @JoinColumn({ name: 'idLichTruc' })
  lichTruc?: LichTruc;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  maDonVi?: string;
  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'code' })
  donVi?: Organization;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  maLoaiHinhTruc?: string;
  @ManyToOne(() => DMLoaiHinhTruc)
  @JoinColumn({ name: 'maLoaiHinhTruc' })
  loaiHinhTruc?: DMLoaiHinhTruc;

  @Column()
  vai?: string;

  @Column()
  eqn?: string;

  @Column()
  stt?: number;

  @Column()
  ghiChu?: string;

  @BeforeInsert()
  @BeforeUpdate()
  updateFullText() {
    this.fullText = [this.ghiChu].filter(Boolean).join(' ').trim();
  }
}
