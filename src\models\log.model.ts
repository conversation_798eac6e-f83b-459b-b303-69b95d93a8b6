import { Column, <PERSON>tity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import EntityModel from './entity.model';
import { Type } from './type.model';
import { User } from './user.model';

@Entity('logs')
export class Log extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'nvarchar', length: 2000, nullable: false })
  content?: string;

  @Column({ type: 'nvarchar', length: 50, nullable: false })
  ip?: string;

  @ManyToOne(() => Type, (type) => type.id)
  type?: Type;

  @ManyToOne(() => User, (user) => user.id)
  user?: User;

  @Column({ nullable: false })
  public typeId!: number;

  @Column({ nullable: false })
  public userId!: number;
}
