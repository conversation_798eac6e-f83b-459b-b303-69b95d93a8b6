import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinC<PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DMLoaiHinhTruc } from './dmLoaiHinhTruc.model';
import EntityModel from './entity.model';

@Entity()
export class LichTruc extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  ten?: string;

  @Column({ type: 'date' })
  ngayKi?: Date;

  @Column()
  thang?: number;

  @Column()
  nam?: number;

  @Column()
  thuTruong?: string;

  @Column()
  noiDung?: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  maLoaiHinhTruc?: string;
  @ManyToOne(() => DMLoaiHinhTruc)
  @JoinColumn({ name: 'maLoaiHinhTruc' })
  loaiHinhTruc?: DMLoaiHinhTruc;

  @BeforeInsert()
  @BeforeUpdate()
  updateFullText() {
    this.fullText = [this.ten, this.thuTruong, this.noiDung]
      .filter(Boolean)
      .join(' ')
      .trim();
  }
}
