import { eQN, Guest, IdentifyCard, Organization, Type, User } from '../models';
import {
  ICreatePreRegisteredGuestBody,
  ISearchPreRegisteredGuestQuery,
} from './pre-registered-guest';
import { database } from '../config';
import { ERROR } from '../utils/error';
import { LOG, TYPE_SCOPES } from '../constants';
import { insertLog } from '../logs/logs.service';
import { PreRegisteredGuest } from '../models/pre-registered-guest.model';
import { IApiError } from '../types/validation';
import dayjs from 'dayjs';
import { ISearchQuery } from '../types/req';
import { createLinks } from '../utils/pagination';
import { In, SelectQueryBuilder } from 'typeorm';
import { checkArrayQuery } from '../utils/array';

const verifyPreRegisteredGuestData = async (
  data: ICreatePreRegisteredGuestBody,
) => {
  const organizationRepo = database.getRepository(Organization);
  const typeRepo = database.getRepository(Type);
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);
  const guestRepo = database.getRepository(Guest);
  const identifyCardRepo = database.getRepository(IdentifyCard);
  const eQNRepo = database.getRepository(eQN);

  const errors: IApiError['errors'] = [];

  // kiểm tra mục đích làm việc
  if (data.purposeCategoryId) {
    const purposeCategory = await typeRepo.findOne({
      where: {
        id: data.purposeCategoryId,
        scope: TYPE_SCOPES.PURPOSE_CATEGORIES,
      },
    });
    if (!purposeCategory) {
      errors.push({
        field: 'purposeCategoryId',
        value: `purposeCategoryId:${data.purposeCategoryId}`,
        message: 'Không tìm thấy loại mục đích làm việc.',
      });
    }
  }

  // Kiểm tra xem có tồn tại bản ghi cha không khi là khách đoàn
  if (data?.parentId) {
    const parentGuestLog = await preRegisteredGuestRepo.findOne({
      where: { id: data.parentId },
    });
    if (!parentGuestLog) {
      errors.push({
        field: 'parentId',
        value: `parentId:${data.parentId}`,
        message: 'Không tìm thấy thông tin đoàn khách.',
      });
    } else if (parentGuestLog.purposeCategoryId) {
      data.purposeCategoryId = parentGuestLog.purposeCategoryId;
    }
  }

  // Kiểm tra người tiếp khách
  const meetWho = await eQNRepo.findOne({
    where: { id: data.meetWhoId },
  });

  data.meetOrgCode = meetWho?.orgCode || '';

  if (!meetWho) {
    errors.push({
      field: 'meetWhoId',
      value: `meetWhoId:${data.meetWhoId}`,
      message: 'Không tìm thấy người tiếp khách.',
    });
  }

  // kiểm tra đơn vị của khách
  if (data?.guestCodeOrg) {
    const guestOrg = await organizationRepo.findOne({
      where: { code: data.guestCodeOrg },
    });
    if (!guestOrg) {
      errors.push({
        field: 'guestCodeOrg',
        value: `guestCodeOrg:${data.guestCodeOrg}`,
        message: 'Không tìm thấy đơn vị của khách.',
      });
    }
  }

  // kiểm tra loại khách
  const guestType = await typeRepo.findOne({
    where: { id: data.guestTypeId, scope: TYPE_SCOPES.GUEST_TYPES },
  });

  if (!guestType) {
    errors.push({
      field: 'guestTypeId',
      value: `guestTypeId:${data.guestTypeId}`,
      message: 'Không tìm loại khách.',
    });
  }

  const { identifyCard, ...guest } = data.guest;
  let guestData: Guest | null;

  if (guest?.id) {
    guestData = await guestRepo.findOne({
      where: { id: guest.id },
    });
    if (!guestData) {
      errors.push({
        field: 'guestId',
        value: `guestId:${data.guest.id}`,
        message: 'Không tìm thấy thông tin khách.',
      });
    }
    await guestRepo.save({ ...guestData, ...guest });
  } else {
    //TODO: sau này check thêm điều kiện sđt hoặc gì để kiểm tra xem có phải khách mới hay không
    const newGuest = guestRepo.create(guest);
    guestData = await guestRepo.save(newGuest);
  }

  const identifyDocumentType = await typeRepo.findOne({
    where: {
      id: data?.guest?.identifyCard?.documentTypeId || 0,
      scope: TYPE_SCOPES.IDENTIFY_DOCUMENT_TYPES,
    },
  });

  if (!identifyDocumentType) {
    errors.push({
      field: 'documentTypeId',
      value: `documentTypeId:${data?.guest?.identifyCard?.documentTypeId}`,
      message: 'Không tìm loại giấy tờ tùy thân.',
    });
  }

  const identifyCardData = await identifyCardRepo.findOne({
    where: {
      documentTypeId: data?.guest?.identifyCard?.documentTypeId,
      guestId: guestData?.id,
    },
  });

  if (!identifyCardData) {
    const newIdentify = identifyCardRepo.create({
      ...identifyCard,
      guestId: guestData?.id,
    });
    await identifyCardRepo.save(newIdentify);
  } else {
    await identifyCardRepo.update(identifyCardData.id as number, identifyCard);
  }
  data.guest.id = guestData?.id as any; // eslint-disable-line @typescript-eslint/no-explicit-any

  return errors;
};

export const createPreRegisteredGuest = async (
  data: ICreatePreRegisteredGuestBody,
  user: User,
  ip?: string,
) => {
  const preRegisterGuestRepo = database.getRepository(PreRegisteredGuest);
  const errors = await verifyPreRegisteredGuestData(data);

  const sameGuestCardId = await preRegisterGuestRepo.findOne({
    where: {
      guestCardId: data.guestCardId,
      status: true,
    },
  });

  if (sameGuestCardId) {
    errors.push({
      field: 'guestCardId',
      value: `guestCardId:${data.guestCardId}`,
      message: 'Mã thẻ khách đang được sử dụng.',
    });
  }

  if (errors && errors.length > 0) {
    const error = {
      part: 'body',
      code: ERROR.BAD_REQUEST,
      message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
      errors,
    };

    return { error };
  }

  const { guest, ...preRegisteredGuestData } = data;

  let newPreRegisteredGuest = preRegisterGuestRepo.create({
    ...preRegisteredGuestData,
    checkInTime: new Date(),
    guestId: guest.id,
    status: true,
    codeOrg: user.manageOrgCode,
    guestOrganizationName: guest.guestOrganizationName,
  });

  newPreRegisteredGuest = await preRegisterGuestRepo.save(
    newPreRegisteredGuest,
  );

  await insertLog({
    typeId: LOG.CREATE,
    userId: user.id,
    content:
      'Tạo mới bản ghi pre-registered guest: ' + newPreRegisteredGuest.id,
    ip,
  });

  return { message: 'Created successfully', data: newPreRegisteredGuest };
};

export const editPreRegisteredGuest = async (
  id: number,
  data: ICreatePreRegisteredGuestBody,
  user: User,
  ip?: string,
) => {
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);

  const preRegisteredGuest = await preRegisteredGuestRepo.findOne({
    where: { id, codeOrg: user.manageOrgCode },
  });

  if (!preRegisteredGuest) {
    throw new Error(ERROR.PRE_REGISTERED_GUEST_NOT_FOUND);
  }

  const errors = await verifyPreRegisteredGuestData(data);

  if (errors && errors.length > 0) {
    const error = {
      part: 'body',
      code: ERROR.BAD_REQUEST,
      message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
      errors,
    };

    return { error };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { guest, ...preRegisteredGuestData } = data;

  await preRegisteredGuestRepo.save({
    ...preRegisteredGuest,
    ...preRegisteredGuestData,
  });

  await insertLog({
    typeId: LOG.UPDATE,
    userId: user.id,
    content: 'Cập nhật bản ghi pre-registered guest: ' + preRegisteredGuest.id,
    ip,
  });

  return { message: 'Updated successfully' };
};

export const returnCard = async (id: number, user: User, ip?: string) => {
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);
  const preRegisteredGuest = await preRegisteredGuestRepo.findOne({
    where: { id, codeOrg: user.manageOrgCode },
    relations: ['children'],
  });

  if (!preRegisteredGuest) {
    throw new Error(ERROR.PRE_REGISTERED_GUEST_NOT_FOUND);
  }

  const updateData = {
    status: false,
    returnTime: new Date(),
    durationInside: dayjs(new Date()).diff(
      preRegisteredGuest.checkInTime,
      'minute',
    ),
  };

  await preRegisteredGuestRepo.update(
    preRegisteredGuest.id as number,
    updateData,
  );

  if (
    preRegisteredGuest?.children &&
    preRegisteredGuest?.children?.length > 0
  ) {
    const childrenIds = preRegisteredGuest.children.map((child) => child.id);
    await preRegisteredGuestRepo.update(childrenIds as number[], updateData);
  }

  await insertLog({
    typeId: LOG.UPDATE,
    userId: user.id,
    content: 'Khách trả thẻ: ' + preRegisteredGuest.id,
    ip,
  });

  return { message: 'Returned successfully' };
};

const createGuestLogSearchQuery = (
  qb: SelectQueryBuilder<PreRegisteredGuest>,
  query: ISearchQuery<ISearchPreRegisteredGuestQuery>,
  user: User,
) => {
  qb.where(
    'preRegisteredGuest.parent_id IS NULL AND preRegisteredGuest.code_org LIKE :codeOrg',
    {
      codeOrg: user.manageOrgCode,
    },
  );

  if (query?.guestCardId) {
    qb.andWhere('preRegisteredGuest.guest_card_id LIKE :guestCardId', {
      guestCardId: `%${query.guestCardId}%`,
    });
  }

  if (query?.identificationNumber) {
    qb.andWhere(
      'identifyCards.identification_number LIKE :identificationNumber',
      {
        identificationNumber: `%${query.identificationNumber}%`,
      },
    );
  }

  if (query.inDateFrom) {
    qb.andWhere('preRegisteredGuest.check_in_time >= :fromDate', {
      fromDate: new Date(query.inDateFrom),
    });
  }

  if (query.inDateTo) {
    qb.andWhere('preRegisteredGuest.check_in_time <= :toDate', {
      toDate: new Date(query.inDateTo),
    });
  }

  if (query.estimatedOutTimeFrom) {
    qb.andWhere('preRegisteredGuest.estimated_out_time >= :fromDate', {
      fromDate: new Date(query.estimatedOutTimeFrom),
    });
  }

  if (query.estimatedOutTimeTo) {
    qb.andWhere('preRegisteredGuest.estimated_out_time <= :toDate', {
      toDate: new Date(query.estimatedOutTimeTo),
    });
  }

  if (query?.purposeCategoryIds) {
    qb.andWhere(
      'preRegisteredGuest.purpose_category_id IN (:...purposeCategoryIds)',
      {
        purposeCategoryIds: checkArrayQuery(query.purposeCategoryIds),
      },
    );
  }

  if (query?.search) {
    qb.andWhere('preRegisteredGuest.fullText LIKE :search', {
      search: `%${query.search}%`,
    });
  }

  if (query.guestName) {
    qb.andWhere('guest.fullName like :guestName', {
      guestName: `%${query.guestName}%`,
    });
  }

  if (query.guestIdentityTypeId) {
    qb.andWhere('identifyCards.document_type_id like :documentType', {
      documentType: query.guestIdentityTypeId,
    });
  }

  if (query.meetWhoId) {
    qb.andWhere('preRegisteredGuest.meet_who_id like :meetWhoId', {
      meetWhoId: query.meetWhoId,
    });
  }

  if (query.meetOrgCode) {
    qb.andWhere('preRegisteredGuest.meet_org_code like :meetOrgCode', {
      meetOrgCode: query.meetOrgCode,
    });
  }

  if (query.guestTypeIds) {
    qb.andWhere('preRegisteredGuest.guest_type_id IN (:...guestTypeIds)', {
      guestTypeIds: checkArrayQuery(query.guestTypeIds),
    });
  }
};

export const searchPreRegisteredGuest = async (
  query: ISearchQuery<ISearchPreRegisteredGuestQuery>,
  user: User,
) => {
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);

  const qb = preRegisteredGuestRepo
    .createQueryBuilder('preRegisteredGuest')
    .leftJoinAndSelect('preRegisteredGuest.guest', 'guest')
    .leftJoinAndSelect('preRegisteredGuest.purposeCategory', 'purposeCategory')
    .leftJoinAndSelect('preRegisteredGuest.guestType', 'guestType')
    .leftJoinAndSelect('preRegisteredGuest.meetWho', 'meetWho')
    .leftJoinAndSelect(
      'preRegisteredGuest.meetOrganization',
      'meetOrganization',
    )
    .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
    .leftJoinAndSelect('identifyCards.documentType', 'documentType');

  createGuestLogSearchQuery(qb, query, user);

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit).orderBy('preRegisteredGuest.createdAt', 'DESC');

  const [data, total] = await qb.getManyAndCount();

  const links = createLinks(
    '/pre-registered-guests?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

export const preRegisteredGuestDetail = async (id: number, user: User) => {
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);

  const preRegisteredGuest = await preRegisteredGuestRepo.findOne({
    where: { id, codeOrg: user.manageOrgCode },
    relations: [
      'purposeCategory',
      'guestType',
      'meetWho',
      'meetOrganization',
      'guest',
      'guest.identifyCards',
      'guest.identifyCards.documentType',
      'children',
      'children.guest',
      'children.guest.identifyCards.documentType',
    ],
  });

  if (!preRegisteredGuest) {
    throw new Error(ERROR.PRE_REGISTERED_GUEST_NOT_FOUND);
  }

  return preRegisteredGuest;
};

export const preRegisteredGuestDetailByGuestCardIdActive = async (
  guestCardId: string,
  user: User,
) => {
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);

  const preRegisteredGuest = await preRegisteredGuestRepo.findOne({
    where: { guestCardId, codeOrg: user.manageOrgCode, status: true },
    relations: [
      'purposeCategory',
      'guestType',
      'meetWho',
      'meetOrganization',
      'guest',
      'guest.identifyCards',
      'guest.identifyCards.documentType',
      'parent',
    ],
  });

  if (!preRegisteredGuest) {
    throw new Error(ERROR.PRE_REGISTERED_GUEST_NOT_FOUND);
  }

  return preRegisteredGuest;
};

export const deletePreRegisteredGuest = async (
  ids: number[],
  user: User,
  ip?: string,
) => {
  const preRegisteredGuestRepo = database.getRepository(PreRegisteredGuest);
  const idArr = Array.isArray(ids) ? ids : [ids];
  const preRegisteredGuest = await preRegisteredGuestRepo.find({
    where: {
      id: In(idArr),
      codeOrg: user.manageOrgCode,
    },
    relations: ['children'],
  });

  if (
    preRegisteredGuest.length !== idArr.length ||
    preRegisteredGuest.length === 0
  ) {
    throw new Error(ERROR.PRE_REGISTERED_GUEST_NOT_FOUND);
  }

  const childrenIds =
    preRegisteredGuest.flatMap(
      (item) => item?.children?.map((child) => child.id) || [],
    ) || [];

  await preRegisteredGuestRepo.delete([...(childrenIds as number[]), ...idArr]);

  await insertLog({
    typeId: LOG.DELETE,
    userId: user.id,
    content: 'Xóa các bản ghi của pre-registered guest: ' + ids?.join(', '),
    ip,
  });
  return { message: 'Deleted successfully' };
};
