export type ICreatePreRegisteredGuestBody = {
  parentId?: number;
  guestCodeOrg: string;
  meetWhoId: string;
  meetOrgCode: string;
  guestCardId: string;
  checkInTime: Date;
  estimatedOutTime?: Date;
  guestTypeId: number;
  purposeOfVisit: string;
  purposeCategoryId: number;
  groupName?: string;
  leaderName?: string;
  leaderPosition?: string;
  numberOfVisitors?: number;
  numberOfVehicles?: number;
  notes?: string;
  isLeader?: boolean;
  guest: {
    id: number;
    fullName: string;
    dateOfBirth: Date;
    phoneNumber: string;
    guestCodeOrg: string;
    guestOrganizationName: string;
    avatar?: string;
    sexId?: number;
    notes?: string;
    office?: string;
    occupation?: string;
    permanentAddress?: string;
    nationality?: string;
    identifyCard: {
      documentTypeId: number;
      identificationNumber: string;
      issueDate: Date;
      expiryDate: Date;
      issuingAuthority: string;
      notes?: string;
      images: string[];
    };
  };
};

export type ISearchPreRegisteredGuestQuery = {
  search: string;
  inDateFrom: Date;
  inDateTo: Date;
  estimatedOutTimeFrom: Date;
  estimatedOutTimeTo: Date;
  guestName: string;
  guestIdentityTypeId: number;
  meetWhoId: string;
  meetOrgCode: string;
  guestTypeIds: number[];
  purposeCategoryIds: number[];
  guestCardId: string;
  identificationNumber: string;
};
