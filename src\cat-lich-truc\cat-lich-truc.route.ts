import express from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import { createValidator } from '../utils/validator';
import { createCatLichTrucBanNoiVu } from './cat-lich-truc.controller';
import { eCreate, jBodyCatLichTrucBanNoiVu } from './cat-lich-truc.validation';
const router = express.Router();
router.post(
  '/truc-ban-noi-vu',
  checkAuth,
  createValidator('body', jBodyCatLichTrucBanNoiVu, eCreate),
  createCatLichTrucBanNoiVu,
);
export default router;
