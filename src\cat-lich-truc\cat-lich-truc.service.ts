import { database } from '../config';
import { LOG, MaLoaiHinhTrucParrent } from '../constants';
import { insertLog } from '../logs/logs.service';
import { LichTruc, NgayTruc, User } from '../models';
import { CatLichTruc } from '../models/catLichTruc.model';
import { ERROR } from '../utils/error';
import { ICatLichTrucBanNoiVuBody } from './cat-lich-truc';

export const createCatLichTrucBanNoiVu = async (body: ICatLichTrucBanNoiVuBody, user: User) => {
  const lichTrucRepo = database.getRepository(LichTruc);
  const ngayTrucRepo = database.getRepository(NgayTruc);
  const CatLichTrucRepo = database.getRepository(CatLichTruc);

  const lichTruc = await lichTrucRepo.findOne({
    where: {
      maLoaiHinhTruc: MaLoaiHinhTrucParrent.TBNV,
      thang: body.date.getMonth() + 1,
      nam: body.date.getFullYear(),
    },
  });
  if (!lichTruc) {
    throw new Error(ERROR.LICH_TRUC_NOT_FOUND);
  }

  const ngayTruc = await ngayTrucRepo.findOne({
    where: {
      ngay: body.date.getDate(),
      thang: body.date.getMonth() + 1,
      nam: body.date.getFullYear(),
    },
  });
  if (!ngayTruc) {
    throw new Error(ERROR.NGAY_TRUC_NOT_FOUND);
  }

  const catLichTruc = await CatLichTrucRepo.findOne({
    where: {
      idNgayTruc: ngayTruc.id,
      idLichTruc: lichTruc.id,
      maLoaiHinhTruc: body.maLoaiHinhTruc,
      maDonVi: body.maDonVi,
    },
    withDeleted: true,
  });

  // const repoEQN = database.getRepository(eQN);
  // const loaiHinhTrucRepo = database.getRepository(DMLoaiHinhTruc);

  // const exitstedEqn = await repoEQN.findOne({
  //   where: { id: eqn },
  //   withDeleted: true,
  // });
  // if (!exitstedEqn) {
  //   throw new Error(ERROR.DATA_NOT_FOUND);
  // }

  // const loaiHinhTrucs = await loaiHinhTrucRepo.find({
  //   withDeleted: true,
  // });
  // if (loaiHinhTrucs.length === 0) {
  //   throw new Error(ERROR.LOAI_HINH_TRUC_NOT_FOUND);
  // }

  // const cauHinhEqnLoaiHinhTrucs = await Promise.all(
  //   loaiHinhTrucs.map(async (loaiHinhTruc) => {
  //     let cauHinhEqnLoaiHinhTruc = await repo.findOne({
  //       where: { eqn: eqn, maLoaiHinhTruc: loaiHinhTruc.ma },
  //       withDeleted: true,
  //     });
  //     if (cauHinhEqnLoaiHinhTruc) {
  //       return cauHinhEqnLoaiHinhTruc;
  //     } else {
  //       cauHinhEqnLoaiHinhTruc = new CauHinhEqnLoaiHinhTruc();
  //       cauHinhEqnLoaiHinhTruc.eQN = exitstedEqn;
  //       cauHinhEqnLoaiHinhTruc.value = '1';
  //       cauHinhEqnLoaiHinhTruc.maLoaiHinhTruc = loaiHinhTruc.ma;
  //       cauHinhEqnLoaiHinhTruc.loaiHinhTruc = loaiHinhTruc;
  //     }
  //     const newCauHinhEqnLoaiHinhTruc = repo.create(cauHinhEqnLoaiHinhTruc);
  //     return repo.save(newCauHinhEqnLoaiHinhTruc);
  //   }),
  // );

  await insertLog({
    ...(user && {
      content: `Tạo mới cắt lịch trực ${body.maLoaiHinhTruc}`,
    }),
    ip: '127.0.0.1',
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return { data: cauHinhEqnLoaiHinhTrucs };
};
