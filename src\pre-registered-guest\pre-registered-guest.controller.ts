import { NextFunction, Request, Response } from 'express';
import {
  ICreatePreRegisteredGuestBody,
  ISearchPreRegisteredGuestQuery,
} from './pre-registered-guest';
import { User } from '../models';
import * as preRegisteredGuestService from './pre-registered-guest.service';
import { ERROR } from '../utils/error';
import { status } from 'http-status';
import { ISearchQuery } from '../types/req';

export const createPreRegisteredGuest = async (
  req: Request & { body: ICreatePreRegisteredGuestBody },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await preRegisteredGuestService.createPreRegisteredGuest(
      req.body,
      user as User,
      req.ip,
    );
    if (result.error) {
      return res.status(status.BAD_REQUEST).json(result.error);
    }
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const editPreRegisteredGuest = async (
  req: Request & { body: ICreatePreRegisteredGuestBody } & {
    params: { id: number };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await preRegisteredGuestService.editPreRegisteredGuest(
      req?.params?.id,
      req.body,
      user as User,
      req.ip,
    );
    if (result.error) {
      return res.status(status.BAD_REQUEST).json(result.error);
    }
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const returnCard = async (
  req: Request & { body: ICreatePreRegisteredGuestBody } & {
    params: { id: number };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await preRegisteredGuestService.returnCard(
      req?.params?.id,
      user as User,
      req.ip,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const searchPreRegisteredGuest = async (
  req: Request & { query: ISearchQuery<ISearchPreRegisteredGuestQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await preRegisteredGuestService.searchPreRegisteredGuest(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const deletePreRegisteredGuest = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await preRegisteredGuestService.deletePreRegisteredGuest(
      req.body?.ids,
      user as User,
      req.ip,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Có 1 số dữ liệu bạn không thể xóa',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const preRegisteredGuestDetail = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await preRegisteredGuestService.preRegisteredGuestDetail(
      req.params.id,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Không tìm thấy thông tin nhật ký khách',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const preRegisteredGuestDetailByGuestCardIdActive = async (
  req: Request & { params: { guestCardId: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result =
      await preRegisteredGuestService.preRegisteredGuestDetailByGuestCardIdActive(
        req.params.guestCardId,
        user as User,
      );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Không tìm thấy thông tin nhật ký khách',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};
